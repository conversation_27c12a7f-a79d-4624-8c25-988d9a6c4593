<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGradient512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chartGradient512" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="80" fill="url(#bgGradient512)"/>
  
  <!-- Chart lines with enhanced design -->
  <g stroke-width="8" stroke-linecap="round" stroke-linejoin="round" filter="url(#glow)">
    <!-- Primary trend line -->
    <path d="M80 300L120 256L160 280L200 236L240 256L280 212L320 236L360 192L400 212L440 168" stroke="#3b82f6" opacity="0.9"/>
    <!-- Secondary trend line -->
    <path d="M80 340L120 296L160 320L200 276L240 296L280 252L320 276L360 232L400 252L440 208" stroke="#10b981" opacity="0.8"/>
    <!-- Tertiary trend line -->
    <path d="M80 380L120 336L160 360L200 316L240 336L280 292L320 316L360 272L400 292L440 248" stroke="#f59e0b" opacity="0.7"/>
    
    <!-- Volume bars -->
    <g stroke="#64748b" stroke-width="6" opacity="0.6">
      <line x1="100" y1="420" x2="100" y2="400"/>
      <line x1="140" y1="420" x2="140" y2="380"/>
      <line x1="180" y1="420" x2="180" y2="390"/>
      <line x1="220" y1="420" x2="220" y2="370"/>
      <line x1="260" y1="420" x2="260" y2="385"/>
      <line x1="300" y1="420" x2="300" y2="365"/>
      <line x1="340" y1="420" x2="340" y2="380"/>
      <line x1="380" y1="420" x2="380" y2="360"/>
      <line x1="420" y1="420" x2="420" y2="375"/>
    </g>
  </g>
  
  <!-- Central logo element -->
  <circle cx="256" cy="256" r="60" fill="#3b82f6" opacity="0.2"/>
  <circle cx="256" cy="256" r="32" fill="#3b82f6" filter="url(#glow)"/>
  
  <!-- Brand text -->
  <text x="256" y="460" text-anchor="middle" fill="#ffffff" font-family="system-ui, -apple-system, sans-serif" font-size="36" font-weight="600" opacity="0.9">AlgoTrader</text>
</svg>
