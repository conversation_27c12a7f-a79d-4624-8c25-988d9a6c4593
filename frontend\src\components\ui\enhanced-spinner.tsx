'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const spinnerVariants = cva(
  "animate-spin",
  {
    variants: {
      variant: {
        default: "border-primary",
        secondary: "border-secondary",
        success: "border-success",
        warning: "border-warning",
        danger: "border-destructive",
        info: "border-info",
      },
      size: {
        xs: "h-3 w-3 border",
        sm: "h-4 w-4 border",
        default: "h-6 w-6 border-2",
        lg: "h-8 w-8 border-2",
        xl: "h-12 w-12 border-2",
        "2xl": "h-16 w-16 border-4",
      },
      speed: {
        slow: "animate-spin-slow",
        default: "animate-spin",
        fast: "animate-spin-fast",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      speed: "default",
    },
  }
);

const pulseVariants = cva(
  "animate-pulse rounded-full",
  {
    variants: {
      variant: {
        default: "bg-primary",
        secondary: "bg-secondary",
        success: "bg-success",
        warning: "bg-warning",
        danger: "bg-destructive",
        info: "bg-info",
      },
      size: {
        xs: "h-3 w-3",
        sm: "h-4 w-4",
        default: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-12 w-12",
        "2xl": "h-16 w-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface EnhancedSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  type?: 'spinner' | 'pulse' | 'dots' | 'bars';
  label?: string;
  showLabel?: boolean;
}

/**
 * Enhanced Spinner component with multiple animation types and variants
 * 
 * @example
 * ```tsx
 * <EnhancedSpinner />
 * <EnhancedSpinner type="pulse" variant="success" size="lg" />
 * <EnhancedSpinner type="dots" label="Loading..." showLabel />
 * ```
 */
export const EnhancedSpinner: React.FC<EnhancedSpinnerProps> = ({
  className,
  variant,
  size,
  speed,
  type = 'spinner',
  label = 'Loading...',
  showLabel = false,
  ...props
}) => {
  const renderSpinner = () => {
    switch (type) {
      case 'pulse':
        return (
          <div className={cn(pulseVariants({ variant, size }), className)} />
        );
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  pulseVariants({ variant, size }),
                  "animate-bounce"
                )}
                style={{
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '0.6s'
                }}
              />
            ))}
          </div>
        );
      
      case 'bars':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={cn(
                  "animate-pulse",
                  variant === 'default' && "bg-primary",
                  variant === 'secondary' && "bg-secondary",
                  variant === 'success' && "bg-success",
                  variant === 'warning' && "bg-warning",
                  variant === 'danger' && "bg-destructive",
                  variant === 'info' && "bg-info",
                  size === 'xs' && "w-1 h-3",
                  size === 'sm' && "w-1 h-4",
                  size === 'default' && "w-1 h-6",
                  size === 'lg' && "w-1.5 h-8",
                  size === 'xl' && "w-2 h-12",
                  size === '2xl' && "w-3 h-16",
                )}
                style={{
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '1s'
                }}
              />
            ))}
          </div>
        );
      
      default:
        return (
          <div
            className={cn(
              spinnerVariants({ variant, size, speed }),
              "rounded-full border-transparent border-t-current",
              className
            )}
          />
        );
    }
  };

  return (
    <div
      className={cn(
        "flex items-center justify-center",
        showLabel && "flex-col space-y-2"
      )}
      role="status"
      aria-label={label}
      {...props}
    >
      {renderSpinner()}
      {showLabel && (
        <span className={cn(
          "text-sm text-muted-foreground",
          size === 'xs' && "text-xs",
          size === 'sm' && "text-xs",
          size === 'lg' && "text-base",
          size === 'xl' && "text-lg",
          size === '2xl' && "text-xl",
        )}>
          {label}
        </span>
      )}
    </div>
  );
};

export default EnhancedSpinner;
