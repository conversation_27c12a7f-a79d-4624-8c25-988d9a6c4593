'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { EnhancedAuthLayout } from '@/components/auth/enhanced-auth-layout';
import { Alert } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { EnhancedSpinner } from '@/components/ui/enhanced-spinner';
import { Progress } from '@/components/ui/progress';
import { FiUser, FiMail, FiLock, FiEye, FiEyeOff, FiAlertCircle, FiCheck, FiUserPlus } from 'react-icons/fi';

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

interface FormErrors {
  name?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  agreeToTerms?: string;
  general?: string;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
}

/**
 * Enhanced Registration Page with comprehensive validation and user experience
 */
export default function RegisterPage() {
  const router = useRouter();
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<RegisterFormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    feedback: [],
    color: 'bg-gray-200',
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Clear errors when form data changes
  useEffect(() => {
    if (error) {
      clearError();
    }
    setFormErrors({});
  }, [formData, error, clearError]);

  // Calculate password strength
  useEffect(() => {
    const calculatePasswordStrength = (password: string): PasswordStrength => {
      if (!password) {
        return { score: 0, feedback: [], color: 'bg-gray-200' };
      }

      let score = 0;
      const feedback: string[] = [];

      // Length check
      if (password.length >= 8) {
        score += 25;
      } else {
        feedback.push('At least 8 characters');
      }

      // Uppercase check
      if (/[A-Z]/.test(password)) {
        score += 25;
      } else {
        feedback.push('One uppercase letter');
      }

      // Lowercase check
      if (/[a-z]/.test(password)) {
        score += 25;
      } else {
        feedback.push('One lowercase letter');
      }

      // Number or special character check
      if (/[\d\W]/.test(password)) {
        score += 25;
      } else {
        feedback.push('One number or special character');
      }

      let color = 'bg-red-500';
      if (score >= 75) color = 'bg-green-500';
      else if (score >= 50) color = 'bg-yellow-500';
      else if (score >= 25) color = 'bg-orange-500';

      return { score, feedback, color };
    };

    setPasswordStrength(calculatePasswordStrength(formData.password));
  }, [formData.password]);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      errors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (passwordStrength.score < 50) {
      errors.password = 'Password is too weak';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      errors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await register(
        formData.name,
        formData.email,
        formData.password,
        formData.confirmPassword
      );

      if (success) {
        router.push('/dashboard');
      }
    } catch (err) {
      // Error is handled by AuthContext
      console.error('Registration error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = (field: 'password' | 'confirmPassword') => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  if (isLoading) {
    return (
      <EnhancedAuthLayout
        title="Loading..."
        showLogo={false}
        showBackToHome={false}
        backgroundVariant="minimal"
      >
        <div className="flex justify-center py-8">
          <EnhancedSpinner size="lg" showLabel label="Loading..." />
        </div>
      </EnhancedAuthLayout>
    );
  }

  return (
    <EnhancedAuthLayout
      title="Join AlgoTrader"
      subtitle="Create your account and start your algorithmic trading journey"
      backgroundVariant="gradient"
      cardVariant="glass"
    >
      {error && (
        <Alert variant="destructive" className="mb-6">
          <FiAlertCircle className="h-4 w-4" />
          <span>{error.message}</span>
        </Alert>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <EnhancedInput
          id="name"
          name="name"
          type="text"
          label="Full Name"
          autoComplete="name"
          required
          value={formData.name}
          onChange={handleInputChange}
          leftIcon={<FiUser className="h-4 w-4" />}
          error={formErrors.name}
          placeholder="Enter your full name"
          size="lg"
          disabled={isSubmitting}
        />

        <EnhancedInput
          id="email"
          name="email"
          type="email"
          label="Email Address"
          autoComplete="email"
          required
          value={formData.email}
          onChange={handleInputChange}
          leftIcon={<FiMail className="h-4 w-4" />}
          error={formErrors.email}
          placeholder="Enter your email address"
          size="lg"
          disabled={isSubmitting}
        />

        <div className="space-y-4">
          <EnhancedInput
            id="password"
            name="password"
            type="password"
            label="Password"
            autoComplete="new-password"
            required
            value={formData.password}
            onChange={handleInputChange}
            leftIcon={<FiLock className="h-4 w-4" />}
            error={formErrors.password}
            placeholder="Create a strong password"
            showPasswordToggle
            size="lg"
            disabled={isSubmitting}
          />

          {/* Password Strength Indicator */}
          {formData.password && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Password strength</span>
                <span className="text-xs text-muted-foreground">{passwordStrength.score}%</span>
              </div>
              <Progress value={passwordStrength.score} className="h-2" />
              {passwordStrength.feedback.length > 0 && (
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">Missing requirements:</p>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {passwordStrength.feedback.map((item, index) => (
                      <li key={index} className="flex items-center space-x-1">
                        <span className="w-1 h-1 bg-muted-foreground rounded-full" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="space-y-4">
          <EnhancedInput
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            label="Confirm Password"
            autoComplete="new-password"
            required
            value={formData.confirmPassword}
            onChange={handleInputChange}
            leftIcon={<FiLock className="h-4 w-4" />}
            error={formErrors.confirmPassword}
            placeholder="Confirm your password"
            showPasswordToggle
            size="lg"
            disabled={isSubmitting}
          />

          {/* Password Match Indicator */}
          {formData.confirmPassword && formData.password && (
            <div className="flex items-center space-x-2">
              {formData.password === formData.confirmPassword ? (
                <>
                  <FiCheck className="h-4 w-4 text-success" />
                  <span className="text-xs text-success">Passwords match</span>
                </>
              ) : (
                <>
                  <FiAlertCircle className="h-4 w-4 text-destructive" />
                  <span className="text-xs text-destructive">Passwords do not match</span>
                </>
              )}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-start space-x-2">
            <Checkbox
              id="agreeToTerms"
              name="agreeToTerms"
              checked={formData.agreeToTerms}
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="mt-1"
            />
            <label htmlFor="agreeToTerms" className="text-sm text-foreground leading-relaxed">
              I agree to the{' '}
              <Link href="/terms" className="text-primary hover:text-primary/80 font-medium">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="/privacy" className="text-primary hover:text-primary/80 font-medium">
                Privacy Policy
              </Link>
            </label>
          </div>
          {formErrors.agreeToTerms && (
            <p className="text-xs text-destructive flex items-center space-x-1">
              <FiAlertCircle className="h-3 w-3" />
              <span>{formErrors.agreeToTerms}</span>
            </p>
          )}
        </div>

        <EnhancedButton
          type="submit"
          variant="gradient"
          size="lg"
          className="w-full"
          isLoading={isSubmitting}
          loadingText="Creating account..."
          leftIcon={!isSubmitting ? <FiUserPlus className="h-4 w-4" /> : undefined}
          disabled={passwordStrength.score < 50}
          ripple
        >
          Create Account
        </EnhancedButton>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link
              href="/auth/login"
              className="font-medium text-primary hover:text-primary/80 transition-colors"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </form>
    </EnhancedAuthLayout>
  );
}
