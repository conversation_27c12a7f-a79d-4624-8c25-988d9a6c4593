'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { EnhancedAuthLayout } from '@/components/auth/enhanced-auth-layout';
import { Alert } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { EnhancedSpinner } from '@/components/ui/enhanced-spinner';
import { FiMail, FiLock, FiEye, FiEyeOff, FiAlertCircle, FiLogIn } from 'react-icons/fi';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

/**
 * Enhanced Login Page with comprehensive authentication features
 */
export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const redirectTo = searchParams.get('redirect') || '/dashboard';
      router.push(redirectTo);
    }
  }, [isAuthenticated, router, searchParams]);

  // Clear errors when form data changes
  useEffect(() => {
    if (error) {
      clearError();
    }
    setFormErrors({});
  }, [formData, error, clearError]);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await login(formData.email, formData.password, formData.rememberMe);

      if (success) {
        const redirectTo = searchParams.get('redirect') || '/dashboard';
        router.push(redirectTo);
      }
    } catch (err) {
      // Error is handled by AuthContext
      console.error('Login error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  if (isLoading) {
    return (
      <EnhancedAuthLayout
        title="Loading..."
        showLogo={false}
        showBackToHome={false}
        backgroundVariant="minimal"
      >
        <div className="flex justify-center py-8">
          <EnhancedSpinner size="lg" showLabel label="Loading..." />
        </div>
      </EnhancedAuthLayout>
    );
  }

  return (
    <EnhancedAuthLayout
      title="Welcome Back"
      subtitle="Sign in to your AlgoTrader account"
      backgroundVariant="gradient"
      cardVariant="glass"
    >
      {error && (
        <Alert variant="destructive" className="mb-6">
          <FiAlertCircle className="h-4 w-4" />
          <span>{error.message}</span>
        </Alert>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <EnhancedInput
          id="email"
          name="email"
          type="email"
          label="Email Address"
          autoComplete="email"
          required
          value={formData.email}
          onChange={handleInputChange}
          leftIcon={<FiMail className="h-4 w-4" />}
          error={formErrors.email}
          placeholder="Enter your email address"
          size="lg"
          disabled={isSubmitting}
        />

        <EnhancedInput
          id="password"
          name="password"
          type="password"
          label="Password"
          autoComplete="current-password"
          required
          value={formData.password}
          onChange={handleInputChange}
          leftIcon={<FiLock className="h-4 w-4" />}
          error={formErrors.password}
          placeholder="Enter your password"
          showPasswordToggle
          size="lg"
          disabled={isSubmitting}
        />

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rememberMe"
              name="rememberMe"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
            <label htmlFor="rememberMe" className="text-sm font-medium text-foreground">
              Remember me
            </label>
          </div>

          <Link
            href="/auth/forgot-password"
            className="text-sm font-medium text-primary hover:text-primary/80 transition-colors"
          >
            Forgot password?
          </Link>
        </div>

        <EnhancedButton
          type="submit"
          variant="gradient"
          size="lg"
          className="w-full"
          isLoading={isSubmitting}
          loadingText="Signing in..."
          leftIcon={!isSubmitting ? <FiLogIn className="h-4 w-4" /> : undefined}
          ripple
        >
          Sign In
        </EnhancedButton>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Don't have an account?{' '}
            <Link
              href="/auth/register"
              className="font-medium text-primary hover:text-primary/80 transition-colors"
            >
              Create one now
            </Link>
          </p>
        </div>
      </form>
    </EnhancedAuthLayout>
  );
}
