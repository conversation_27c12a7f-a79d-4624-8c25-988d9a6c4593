'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { <PERSON><PERSON>heck, FiX, FiEye, FiEyeOff, FiAlertCircle, FiInfo } from 'react-icons/fi';

const inputVariants = cva(
  "flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-input focus-visible:ring-ring",
        success: "border-success focus-visible:ring-success",
        error: "border-destructive focus-visible:ring-destructive",
        warning: "border-warning focus-visible:ring-warning",
      },
      size: {
        sm: "h-8 px-2 text-xs",
        default: "h-10 px-3",
        lg: "h-12 px-4 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface EnhancedInputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
  isLoading?: boolean;
  containerClassName?: string;
}

/**
 * Enhanced Input component with validation states, icons, and better UX
 * 
 * @example
 * ```tsx
 * <EnhancedInput label="Email" placeholder="Enter your email" />
 * <EnhancedInput 
 *   label="Password" 
 *   type="password" 
 *   showPasswordToggle 
 *   error="Password is required" 
 * />
 * <EnhancedInput 
 *   label="Username" 
 *   leftIcon={<UserIcon />} 
 *   success="Username is available" 
 * />
 * ```
 */
export const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ 
    className,
    containerClassName,
    variant,
    size,
    type = "text",
    label,
    description,
    error,
    success,
    warning,
    leftIcon,
    rightIcon,
    showPasswordToggle = false,
    isLoading = false,
    id,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    const inputId = id || React.useId();
    
    // Determine the actual input type
    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password')
      : type;
    
    // Determine variant based on validation state
    const currentVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant;
    
    // Get validation message and icon
    const validationMessage = error || success || warning;
    const validationIcon = error ? (
      <FiX className="h-4 w-4 text-destructive" />
    ) : success ? (
      <FiCheck className="h-4 w-4 text-success" />
    ) : warning ? (
      <FiAlertCircle className="h-4 w-4 text-warning" />
    ) : null;

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={cn("space-y-2", containerClassName)}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className={cn(
              "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              error && "text-destructive",
              success && "text-success",
              warning && "text-warning"
            )}
          >
            {label}
            {props.required && <span className="text-destructive ml-1">*</span>}
          </label>
        )}
        
        {/* Description */}
        {description && !validationMessage && (
          <p className="text-xs text-muted-foreground flex items-center gap-1">
            <FiInfo className="h-3 w-3" />
            {description}
          </p>
        )}
        
        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          {/* Input */}
          <input
            type={inputType}
            className={cn(
              inputVariants({ variant: currentVariant, size }),
              leftIcon && "pl-10",
              (rightIcon || showPasswordToggle || validationIcon || isLoading) && "pr-10",
              isFocused && "ring-2 ring-offset-2",
              className
            )}
            ref={ref}
            id={inputId}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />
          
          {/* Right Side Icons */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {/* Loading Spinner */}
            {isLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-muted-foreground border-t-transparent" />
            )}
            
            {/* Validation Icon */}
            {!isLoading && validationIcon}
            
            {/* Password Toggle */}
            {!isLoading && !validationIcon && showPasswordToggle && type === 'password' && (
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="text-muted-foreground hover:text-foreground transition-colors"
                tabIndex={-1}
              >
                {showPassword ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
              </button>
            )}
            
            {/* Right Icon */}
            {!isLoading && !validationIcon && !showPasswordToggle && rightIcon && (
              <div className="text-muted-foreground">
                {rightIcon}
              </div>
            )}
          </div>
        </div>
        
        {/* Validation Message */}
        {validationMessage && (
          <p className={cn(
            "text-xs flex items-center gap-1",
            error && "text-destructive",
            success && "text-success",
            warning && "text-warning"
          )}>
            {validationIcon}
            {validationMessage}
          </p>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

export default EnhancedInput;
