'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Alert } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { FiMail, FiCheckCircle, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';

/**
 * Enhanced Email Verification Page with comprehensive verification functionality
 */
export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    verifyEmail,
    resendVerificationEmail,
    user,
    isAuthenticated,
    isLoading,
    error,
    clearError
  } = useAuth();

  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [resendSuccess, setResendSuccess] = useState(false);

  // Get token from URL params
  useEffect(() => {
    const tokenParam = searchParams?.get('token');
    if (tokenParam) {
      setToken(tokenParam);
      // Automatically verify if token is present
      handleVerification(tokenParam);
    }
  }, [searchParams]);

  // Clear errors when component mounts
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  const handleVerification = async (verificationToken: string) => {
    setIsVerifying(true);
    setIsError(false);

    try {
      const success = await verifyEmail(verificationToken);

      if (success) {
        setIsSuccess(true);
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      } else {
        setIsError(true);
      }
    } catch (err) {
      console.error('Email verification error:', err);
      setIsError(true);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendVerification = async () => {
    if (!user?.email) {
      return;
    }

    setIsResending(true);
    setResendSuccess(false);

    try {
      const success = await resendVerificationEmail();

      if (success) {
        setResendSuccess(true);
      }
    } catch (err) {
      console.error('Resend verification error:', err);
    } finally {
      setIsResending(false);
    }
  };

  if (isLoading || isVerifying) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-sm text-gray-600">
            {isVerifying ? 'Verifying your email...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  // Success state
  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Email verified successfully
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Your email has been verified. You can now access all features.
            </p>
          </div>

          {/* Success Card */}
          <Card className="p-8">
            <div className="text-center space-y-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <FiCheckCircle className="h-6 w-6 text-green-600" />
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900">Verification complete</h3>
                <p className="mt-2 text-sm text-gray-600">
                  Your email address has been successfully verified. You will be redirected to your dashboard shortly.
                </p>
              </div>

              <div className="pt-4">
                <Link href="/dashboard">
                  <Button className="w-full" size="lg">
                    Continue to dashboard
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  // Error state or manual verification needed
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Verify your email
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {isError
              ? 'There was an issue verifying your email address.'
              : 'Please check your email and click the verification link.'
            }
          </p>
        </div>

        {/* Verification Card */}
        <Card className="p-8">
          <div className="text-center space-y-4">
            {isError ? (
              <>
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                  <FiAlertCircle className="h-6 w-6 text-red-600" />
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900">Verification failed</h3>
                  <p className="mt-2 text-sm text-gray-600">
                    The verification link may be invalid or expired. Please try requesting a new verification email.
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                  <FiMail className="h-6 w-6 text-blue-600" />
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900">Check your email</h3>
                  <p className="mt-2 text-sm text-gray-600">
                    We've sent a verification link to{' '}
                    {user?.email && <strong>{user.email}</strong>}
                    {!user?.email && 'your email address'}.
                    Please click the link to verify your account.
                  </p>
                </div>
              </>
            )}

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive" className="text-left">
                <FiAlertCircle className="h-4 w-4" />
                <span>{error.message}</span>
              </Alert>
            )}

            {/* Resend Success Alert */}
            {resendSuccess && (
              <Alert variant="default" className="text-left border-green-200 bg-green-50">
                <FiCheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-green-800">Verification email sent successfully!</span>
              </Alert>
            )}

            {/* Resend Button */}
            {isAuthenticated && user && (
              <div className="pt-4">
                <Button
                  onClick={handleResendVerification}
                  disabled={isResending}
                  variant="outline"
                  className="w-full"
                  size="lg"
                >
                  {isResending ? (
                    <>
                      <Spinner size="sm" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <FiRefreshCw className="mr-2 h-4 w-4" />
                      Resend verification email
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* Help Text */}
            <div className="text-sm text-gray-600">
              <p>Didn't receive the email? Check your spam folder.</p>
              {!isAuthenticated && (
                <p className="mt-2">
                  <Link href="/auth/login" className="text-blue-600 hover:text-blue-500">
                    Sign in to your account
                  </Link>{' '}
                  to resend the verification email.
                </p>
              )}
            </div>

            {/* Back to Dashboard */}
            {isAuthenticated && (
              <div className="pt-4">
                <Link
                  href="/dashboard"
                  className="text-sm text-blue-600 hover:text-blue-500"
                >
                  Continue to dashboard
                </Link>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
