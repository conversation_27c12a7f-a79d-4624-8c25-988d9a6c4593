"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import apiClient, { apiUtils } from '@/api/client';
import sessionManager, { SessionOptions } from '@/utils/session/sessionManager';
import {
  Role,
  Permission,
  hasPermission,
  hasAllPermissions,
  hasAnyPermission,
  isAtLeastRole
} from '@/utils/auth/roles';

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions?: string[];
  preferences?: Record<string, any>;
  createdAt?: string;
  lastLogin?: string;
  emailVerified?: boolean;
  created_at?: string;
  last_login?: string;
  email_verified?: boolean;
}

export interface AuthError {
  message: string;
  field?: string;
  details?: any;
}

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: User;
  expires_in: number;
  token_id?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => Promise<boolean>;
  register: (name: string, email: string, password: string, confirmPassword: string) => Promise<boolean>;
  updateProfile: (name: string, email: string) => Promise<boolean>;
  requestPasswordReset: (email: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string, confirmPassword: string) => Promise<boolean>;
  verifyEmail: (token: string) => Promise<boolean>;
  resendVerificationEmail: () => Promise<boolean>;
  changePassword: (currentPassword: string, newPassword: string, confirmPassword: string) => Promise<boolean>;
  clearError: () => void;
  // Role-based access control methods
  hasRole: (role: Role | Role[]) => boolean;
  hasPermission: (permission: Permission) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  isAtLeastRole: (role: Role) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, // eslint-disable-next-line react-hooks/exhaustive-deps
  []);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Use session manager to check if we have a valid token
        const currentSession = sessionManager.getCurrentSession();
        if (currentSession) {
          const response = await apiClient.get('/auth/me');
          setUser(response.data);

          // Update last activity timestamp
          sessionManager.updateLastActivity();
        }
      } catch (error) {
        // Clear session if authentication check fails
        sessionManager.clearSession();
        console.error('Auth check failed:', apiUtils.handleError(error).message);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();

    // Set up session timeout handler
    sessionManager.onSessionTimeout(() => {
      // Clear user state but don't redirect - the session timeout dialog will handle that
      setUser(null);
    });
  }, // eslint-disable-next-line react-hooks/exhaustive-deps
  []);

  const login = async (email: string, password: string, rememberMe = false): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.post<LoginResponse>('/auth/login', {
        email,
        password,
        remember_me: rememberMe,
        // Include device info for multi-device session management
        device_info: JSON.stringify(sessionManager.getCurrentSession()?.deviceInfo || {})
      });

      const { access_token, refresh_token, expires_in, user: userData, token_id } = response.data;

      // Set session options
      const sessionOptions: SessionOptions = {
        rememberMe,
        // Set session timeout based on remember me preference
        timeout: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 30 * 60 * 1000 // 7 days or 30 minutes
      };

      // Use session manager to store tokens
      sessionManager.setTokens({
        access_token,
        refresh_token,
        expires_in,
        token_id
      }, sessionOptions);

      setUser(userData);
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details,
        field: Array.isArray(errorData.details) ? errorData.details[0]?.field : undefined
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // Get current session ID for logout
      const currentSession = sessionManager.getCurrentSession();

      // Try to call logout endpoint with session ID, but proceed even if it fails
      if (currentSession) {
        await apiClient.post('/auth/logout', {
          session_id: currentSession.id
        }).catch(err => {
          console.warn('Logout API call failed:', err.message);
        });
      }

      // Clear session
      sessionManager.clearSession();
      setUser(null);

      // Redirect to home page
      router.push('/');
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (
    name: string,
    email: string,
    password: string,
    confirmPassword: string
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate password match
      if (password !== confirmPassword) {
        setError({
          message: 'Passwords do not match',
          field: 'confirmPassword'
        });
        return false;
      }

      await apiClient.post('/auth/register', {
        name,
        email,
        password,
        confirm_password: confirmPassword
      });

      // After registration, log the user in
      return await login(email, password);
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details,
        field: Array.isArray(errorData.details) ? errorData.details[0]?.field : undefined
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (name: string, email: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.put<User>('/auth/profile', { name, email });
      setUser(response.data);
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const requestPasswordReset = async (email: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      await apiClient.post('/auth/password-reset', { email });
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (
    token: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate password match
      if (newPassword !== confirmPassword) {
        setError({
          message: 'Passwords do not match',
          field: 'confirmPassword'
        });
        return false;
      }

      await apiClient.post('/auth/password-reset-confirm', {
        token,
        new_password: newPassword,
        confirm_password: confirmPassword
      });
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyEmail = async (token: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      await apiClient.post('/auth/verify-email', { token });

      // If user is logged in, update their status
      if (user) {
        setUser({
          ...user,
          email_verified: true
        });
      }

      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resendVerificationEmail = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        setError({
          message: 'You must be logged in to resend verification email'
        });
        return false;
      }

      await apiClient.post('/auth/resend-verification');
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate password match
      if (newPassword !== confirmPassword) {
        setError({
          message: 'Passwords do not match',
          field: 'confirmPassword'
        });
        return false;
      }

      await apiClient.post('/auth/password-change', {
        current_password: currentPassword,
        new_password: newPassword,
        confirm_password: confirmPassword
      });
      return true;
    } catch (err) {
      const errorData = apiUtils.handleError(err);
      setError({
        message: errorData.message,
        details: errorData.details
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Role-based access control methods
  const hasRole = useCallback(
    (role: Role | Role[]): boolean => {
      if (!user) return false;

      const userRole = user.role as Role;
      const requiredRoles = Array.isArray(role) ? role : [role];

      return requiredRoles.some(r => r === userRole || isAtLeastRole(userRole as Role, r));
    },
    [user]
  );

  const checkPermission = useCallback(
    (permission: Permission): boolean => {
      if (!user) return false;

      // If the backend provides explicit permissions, use those
      if (user.permissions && user.permissions.includes(permission)) {
        return true;
      }

      // Otherwise, use the role-based permission system
      return hasPermission(user.role as Role, permission);
    },
    [user]
  );

  const checkAllPermissions = useCallback(
    (permissions: Permission[]): boolean => {
      if (!user) return false;

      // If the backend provides explicit permissions, use those
      if (user.permissions) {
        return permissions.every(permission => user.permissions!.includes(permission));
      }

      // Otherwise, use the role-based permission system
      return hasAllPermissions(user.role as Role, permissions);
    },
    [user]
  );

  const checkAnyPermission = useCallback(
    (permissions: Permission[]): boolean => {
      if (!user) return false;

      // If the backend provides explicit permissions, use those
      if (user.permissions) {
        return permissions.some(permission => user.permissions!.includes(permission));
      }

      // Otherwise, use the role-based permission system
      return hasAnyPermission(user.role as Role, permissions);
    },
    [user]
  );

  const checkAtLeastRole = useCallback(
    (role: Role): boolean => {
      if (!user) return false;

      const userRole = user.role as Role;
      return userRole === role || isAtLeastRole(userRole, role);
    },
    [user]
  );

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        error,
        login,
        logout,
        register,
        updateProfile,
        requestPasswordReset,
        resetPassword,
        verifyEmail,
        resendVerificationEmail,
        changePassword,
        clearError,
        // Role-based access control methods
        hasRole,
        hasPermission: checkPermission,
        hasAllPermissions: checkAllPermissions,
        hasAnyPermission: checkAnyPermission,
        isAtLeastRole: checkAtLeastRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
