'use client';

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { EnhancedCard, EnhancedCardContent, EnhancedCardHeader } from '@/components/ui/enhanced-card';

interface EnhancedAuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showLogo?: boolean;
  showBackToHome?: boolean;
  backgroundVariant?: 'default' | 'gradient' | 'pattern' | 'minimal';
  cardVariant?: 'default' | 'elevated' | 'glass';
  className?: string;
}

/**
 * Enhanced Authentication Layout component with modern design and animations
 * 
 * @example
 * ```tsx
 * <EnhancedAuthLayout 
 *   title="Sign In" 
 *   subtitle="Welcome back to AlgoTrader"
 *   backgroundVariant="gradient"
 *   cardVariant="glass"
 * >
 *   <LoginForm />
 * </EnhancedAuthLayout>
 * ```
 */
export const EnhancedAuthLayout: React.FC<EnhancedAuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showLogo = true,
  showBackToHome = true,
  backgroundVariant = 'gradient',
  cardVariant = 'elevated',
  className,
}) => {
  const getBackgroundClasses = () => {
    switch (backgroundVariant) {
      case 'gradient':
        return 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900';
      case 'pattern':
        return 'bg-slate-50 dark:bg-slate-900 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-100 via-slate-50 to-slate-100 dark:from-slate-800 dark:via-slate-900 dark:to-slate-900';
      case 'minimal':
        return 'bg-background';
      default:
        return 'bg-slate-50 dark:bg-slate-900';
    }
  };

  const getCardClasses = () => {
    switch (cardVariant) {
      case 'glass':
        return 'backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border-white/20 dark:border-slate-700/20 shadow-2xl';
      case 'elevated':
        return 'shadow-2xl border-0 bg-white dark:bg-slate-900';
      default:
        return '';
    }
  };

  return (
    <div className={cn(
      'min-h-screen flex items-center justify-center p-4 relative overflow-hidden',
      getBackgroundClasses(),
      className
    )}>
      {/* Background decorative elements */}
      {backgroundVariant === 'gradient' && (
        <>
          <div className="absolute top-0 left-0 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-500" />
        </>
      )}
      
      {/* Main content */}
      <div className="w-full max-w-md relative z-10">
        {/* Logo */}
        {showLogo && (
          <div className="text-center mb-8">
            <Link href="/" className="inline-block">
              <div className="flex items-center justify-center space-x-2 group">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-white"
                  >
                    <path
                      d="M3 12L6 9L9 12L12 9L15 12L18 9L21 12"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3 16L6 13L9 16L12 13L15 16L18 13L21 16"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  AlgoTrader
                </span>
              </div>
            </Link>
          </div>
        )}
        
        {/* Auth Card */}
        <EnhancedCard
          variant={cardVariant === 'glass' ? 'ghost' : 'elevated'}
          size="lg"
          animation="subtle"
          className={cn(
            'w-full animate-in fade-in-0 slide-in-from-bottom-4 duration-500',
            getCardClasses()
          )}
        >
          <EnhancedCardHeader className="text-center space-y-2">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">
              {title}
            </h1>
            {subtitle && (
              <p className="text-muted-foreground text-base">
                {subtitle}
              </p>
            )}
          </EnhancedCardHeader>
          
          <EnhancedCardContent className="mt-6">
            {children}
          </EnhancedCardContent>
        </EnhancedCard>
        
        {/* Back to Home */}
        {showBackToHome && (
          <div className="text-center mt-6">
            <Link
              href="/"
              className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 inline-flex items-center space-x-1 group"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                className="group-hover:-translate-x-1 transition-transform duration-200"
              >
                <path
                  d="M19 12H5M12 19L5 12L12 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span>Back to Home</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedAuthLayout;
