{"name": "algotrader-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:html": "jest --coverage --coverageReporters=html --coverageDirectory=coverage", "test:coverage:open": "cross-env OPEN_COVERAGE_REPORT=true jest --coverage", "test:unit": "jest --testPathPattern=tests/unit", "test:unit:coverage": "jest --testPathPattern=tests/unit --coverage --coverageReporters=html --coverageDirectory=coverage", "test:integration": "jest --testPathPattern=tests/integration", "test:integration:simple": "jest --testPathPattern=tests/integration/.*Simple.test.tsx --no-coverage", "test:accessibility": "jest --testPathPattern=tests/accessibility", "test:ci": "npm run test:integration:simple", "test:all": "npm run test:unit && npm run test:integration:simple", "test:e2e": "playwright test", "test:update": "jest --updateSnapshot", "test:clear": "jest --clear<PERSON>ache", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "update-components": "node ../scripts/update-component-imports.js", "update-components:dry": "node ../scripts/update-component-imports.js --dry-run", "update-file-imports": "node ../scripts/update-component-imports-in-file.js", "update-file-imports:dry": "node ../scripts/update-component-imports-in-file.js --dry-run", "find-component-usage": "node ../scripts/find-component-usage.js", "demo-component-integration": "node ../scripts/demo-component-integration.js"}, "dependencies": {"@headlessui/react": "^2.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.1.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "@types/react-grid-layout": "^1.3.5", "@use-gesture/react": "^10.3.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "d3": "^7.8.5", "date-fns": "^2.30.0", "framer-motion": "^12.12.1", "idb-keyval": "^6.2.1", "lightweight-charts": "^4.1.1", "lucide-react": "^0.510.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.48.2", "react-icons": "^4.12.0", "react-intersection-observer": "^9.16.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.10", "reactflow": "^11.11.4", "recharts": "^2.10.3", "sonner": "^1.4.0", "swr": "^2.2.4", "tailwind-merge": "^3.3.0", "uuid": "^9.0.1", "zod": "^3.24.4", "next": "14.0.3", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "devDependencies": {"@axe-core/react": "^4.7.3", "@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@playwright/test": "^1.40.0", "@storybook/addon-a11y": "^7.5.3", "@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/blocks": "^7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.4.3", "@types/d3": "^7.4.3", "@types/jest": "^29.5.2", "@types/jest-axe": "^3.5.9", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/testing-library__jest-dom": "^5.14.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-plugin-jest": "^27.2.2", "eslint-plugin-jest-dom": "^5.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-testing-library": "^5.11.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "storybook": "^7.5.3", "ts-jest": "^29.1.0", "typescript": "^5.3.2"}}