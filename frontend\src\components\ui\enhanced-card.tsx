'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-border",
        elevated: "shadow-lg hover:shadow-xl",
        outlined: "border-2",
        ghost: "border-transparent shadow-none",
        gradient: "bg-gradient-to-br from-card to-card/80 border-border/50",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-10",
      },
      animation: {
        none: "",
        hover: "hover:scale-[1.02] hover:shadow-lg",
        subtle: "hover:shadow-md",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
    },
  }
);

export interface EnhancedCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  loading?: boolean;
  blur?: boolean;
}

/**
 * Enhanced Card component with variants, animations, and better layouts
 * 
 * @example
 * ```tsx
 * <EnhancedCard variant="elevated" animation="hover">
 *   <h3>Card Title</h3>
 *   <p>Card content</p>
 * </EnhancedCard>
 * ```
 */
export const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    header,
    footer,
    loading = false,
    blur = false,
    children,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          cardVariants({ variant, size, animation }),
          loading && "opacity-70 pointer-events-none",
          blur && "backdrop-blur-sm bg-card/80",
          className
        )}
        {...props}
      >
        {/* Header */}
        {header && (
          <div className="mb-4 pb-4 border-b border-border/50">
            {header}
          </div>
        )}
        
        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-card/50 rounded-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
          </div>
        )}
        
        {/* Content */}
        <div className={cn(loading && "relative")}>
          {children}
        </div>
        
        {/* Footer */}
        {footer && (
          <div className="mt-4 pt-4 border-t border-border/50">
            {footer}
          </div>
        )}
      </div>
    );
  }
);

EnhancedCard.displayName = "EnhancedCard";

/**
 * Enhanced Card Header component
 */
export const EnhancedCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5", className)}
    {...props}
  />
));
EnhancedCardHeader.displayName = "EnhancedCardHeader";

/**
 * Enhanced Card Title component
 */
export const EnhancedCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
EnhancedCardTitle.displayName = "EnhancedCardTitle";

/**
 * Enhanced Card Description component
 */
export const EnhancedCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
EnhancedCardDescription.displayName = "EnhancedCardDescription";

/**
 * Enhanced Card Content component
 */
export const EnhancedCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
));
EnhancedCardContent.displayName = "EnhancedCardContent";

/**
 * Enhanced Card Footer component
 */
export const EnhancedCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center", className)}
    {...props}
  />
));
EnhancedCardFooter.displayName = "EnhancedCardFooter";

export default EnhancedCard;
