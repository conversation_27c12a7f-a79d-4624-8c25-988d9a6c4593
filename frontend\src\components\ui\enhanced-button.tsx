'use client';

import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { EnhancedSpinner } from './enhanced-spinner';

const enhancedButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md active:scale-95",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-md active:scale-95",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:shadow-sm active:scale-95",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-sm active:scale-95",
        ghost: "hover:bg-accent hover:text-accent-foreground active:scale-95",
        link: "text-primary underline-offset-4 hover:underline active:scale-95",
        success: "bg-success text-success-foreground hover:bg-success/90 hover:shadow-md active:scale-95",
        warning: "bg-warning text-warning-foreground hover:bg-warning/90 hover:shadow-md active:scale-95",
        info: "bg-info text-info-foreground hover:bg-info/90 hover:shadow-md active:scale-95",
        gradient: "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70 hover:shadow-lg active:scale-95",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-12 rounded-lg px-10 text-base",
        icon: "h-10 w-10",
      },
      animation: {
        none: "",
        pulse: "hover:animate-pulse",
        bounce: "hover:animate-bounce",
        wiggle: "hover:animate-wiggle",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
    },
  }
);

export interface EnhancedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof enhancedButtonVariants> {
  asChild?: boolean;
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  ripple?: boolean;
}

/**
 * Enhanced Button component with loading states, animations, and improved UX
 * 
 * @example
 * ```tsx
 * <EnhancedButton>Default Button</EnhancedButton>
 * <EnhancedButton variant="gradient" size="lg">Gradient Button</EnhancedButton>
 * <EnhancedButton isLoading loadingText="Signing in...">Sign In</EnhancedButton>
 * <EnhancedButton leftIcon={<Icon />} rightIcon={<Icon />}>With Icons</EnhancedButton>
 * ```
 */
export const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    asChild = false, 
    isLoading = false,
    loadingText,
    leftIcon, 
    rightIcon, 
    children, 
    disabled,
    ripple = false,
    onClick,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button";
    const isDisabled = disabled || isLoading;
    const [rippleEffect, setRippleEffect] = React.useState<{ x: number; y: number; id: number } | null>(null);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (ripple && !isDisabled) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        setRippleEffect({ x, y, id: Date.now() });
        
        setTimeout(() => setRippleEffect(null), 600);
      }
      
      if (onClick && !isDisabled) {
        onClick(e);
      }
    };

    return (
      <Comp
        className={cn(enhancedButtonVariants({ variant, size, animation, className }))}
        ref={ref}
        disabled={isDisabled}
        onClick={handleClick}
        {...props}
      >
        {/* Ripple effect */}
        {rippleEffect && (
          <span
            className="absolute rounded-full bg-white/30 animate-ping"
            style={{
              left: rippleEffect.x - 10,
              top: rippleEffect.y - 10,
              width: 20,
              height: 20,
            }}
          />
        )}
        
        {/* Loading spinner */}
        {isLoading && (
          <EnhancedSpinner
            size={size === 'sm' ? 'xs' : size === 'lg' || size === 'xl' ? 'sm' : 'xs'}
            variant="default"
            className="mr-2 text-current"
          />
        )}
        
        {/* Left icon */}
        {!isLoading && leftIcon && (
          <span className="mr-2 flex items-center">
            {leftIcon}
          </span>
        )}
        
        {/* Button content */}
        <span className={cn(
          "flex items-center",
          isLoading && "opacity-70"
        )}>
          {isLoading && loadingText ? loadingText : children}
        </span>
        
        {/* Right icon */}
        {!isLoading && rightIcon && (
          <span className="ml-2 flex items-center">
            {rightIcon}
          </span>
        )}
      </Comp>
    );
  }
);

EnhancedButton.displayName = "EnhancedButton";

export default EnhancedButton;
